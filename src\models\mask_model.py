"""
Mask数据模型
定义标线mask的数据结构和基本操作
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
from enum import Enum
import cv2


class LaneType(Enum):
    """标线类型枚举"""
    # 线性标记
    SINGLE_SOLID = "single_solid"
    DOUBLE_SOLID = "double_solid"
    DASHED = "dashed"
    STOP_LINE = "stop_line"
    GUIDE_LINE = "guide_line"

    # 箭头标记
    LEFT_TURN = "left_turn"
    RIGHT_TURN = "right_turn"
    STRAIGHT = "straight"
    U_TURN = "u_turn"
    LEFT_RIGHT = "left_right"

    # 符号标记
    CROSSWALK = "crosswalk"
    TEXT_MARK = "text_mark"
    GRID_MARK = "grid_mark"
    GUIDE_AREA = "guide_area"


class OcclusionType(Enum):
    """遮挡类型枚举"""
    DYNAMIC = "dynamic"    # 动态遮挡（车辆、行人）
    STATIC = "static"      # 静态遮挡（停车、设施）


@dataclass
class BoundingBox:
    """边界框数据结构"""
    x_min: int
    y_min: int
    x_max: int
    y_max: int

    @property
    def width(self) -> int:
        return self.x_max - self.x_min

    @property
    def height(self) -> int:
        return self.y_max - self.y_min

    @property
    def area(self) -> int:
        return self.width * self.height

    @property
    def center(self) -> Tuple[int, int]:
        return ((self.x_min + self.x_max) // 2, (self.y_min + self.y_max) // 2)


@dataclass
class LaneSegment:
    """标线段数据结构"""
    lane_id: int
    lane_type: LaneType
    mask: np.ndarray  # 二值mask
    bbox: BoundingBox
    confidence: float
    pixels: List[Tuple[int, int]]  # 像素坐标列表

    def get_skeleton(self) -> np.ndarray:
        """提取标线骨架"""
        from skimage.morphology import skeletonize
        return skeletonize(self.mask > 0)

    def get_endpoints(self) -> List[Tuple[int, int]]:
        """获取端点"""
        skeleton = self.get_skeleton()
        # 简化实现：找到骨架的端点
        kernel = np.ones((3, 3), np.uint8)
        neighbors = cv2.filter2D(skeleton.astype(np.uint8), -1, kernel)
        endpoints = np.where((skeleton > 0) & (neighbors == 2))
        return list(zip(endpoints[1], endpoints[0]))  # (x, y)格式

    def get_direction_vector(self) -> Optional[Tuple[float, float]]:
        """获取主方向向量"""
        if len(self.pixels) < 2:
            return None

        # 使用PCA计算主方向
        points = np.array(self.pixels)
        mean_point = np.mean(points, axis=0)
        centered_points = points - mean_point

        # 计算协方差矩阵
        cov_matrix = np.cov(centered_points.T)
        eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)

        # 主方向是最大特征值对应的特征向量
        main_direction = eigenvectors[:, np.argmax(eigenvalues)]
        return tuple(main_direction)


@dataclass
class OcclusionRegion:
    """遮挡区域数据结构"""
    occlusion_id: int
    occlusion_type: OcclusionType
    mask: np.ndarray  # 遮挡区域mask
    bbox: BoundingBox
    confidence: float
    affected_lanes: List[int]  # 受影响的标线ID列表

    def get_overlap_with_lane(self, lane_segment: LaneSegment) -> float:
        """计算与标线段的重合度"""
        intersection = np.logical_and(self.mask, lane_segment.mask)
        union = np.logical_or(self.mask, lane_segment.mask)

        intersection_area = np.sum(intersection)
        union_area = np.sum(union)

        if union_area == 0:
            return 0.0

        return intersection_area / union_area


class LaneMask:
    """标线mask主类"""

    def __init__(self, image_shape: Tuple[int, int]):
        self.height, self.width = image_shape
        self.lane_segments: Dict[int, LaneSegment] = {}
        self.occlusion_regions: Dict[int, OcclusionRegion] = {}
        self.next_lane_id = 1
        self.next_occlusion_id = 1

    def add_lane_segment(self,
                        lane_type: LaneType,
                        mask: np.ndarray,
                        confidence: float = 1.0) -> int:
        """添加标线段"""
        lane_id = self.next_lane_id
        self.next_lane_id += 1

        # 计算边界框
        y_coords, x_coords = np.where(mask > 0)
        if len(x_coords) == 0:
            raise ValueError("Empty mask provided")

        bbox = BoundingBox(
            x_min=int(np.min(x_coords)),
            y_min=int(np.min(y_coords)),
            x_max=int(np.max(x_coords)),
            y_max=int(np.max(y_coords))
        )

        # 提取像素坐标
        pixels = list(zip(x_coords, y_coords))

        lane_segment = LaneSegment(
            lane_id=lane_id,
            lane_type=lane_type,
            mask=mask.copy(),
            bbox=bbox,
            confidence=confidence,
            pixels=pixels
        )

        self.lane_segments[lane_id] = lane_segment
        return lane_id

    def add_occlusion_region(self,
                           occlusion_type: OcclusionType,
                           mask: np.ndarray,
                           confidence: float = 1.0) -> int:
        """添加遮挡区域"""
        occlusion_id = self.next_occlusion_id
        self.next_occlusion_id += 1

        # 计算边界框
        y_coords, x_coords = np.where(mask > 0)
        if len(x_coords) == 0:
            raise ValueError("Empty occlusion mask provided")

        bbox = BoundingBox(
            x_min=int(np.min(x_coords)),
            y_min=int(np.min(y_coords)),
            x_max=int(np.max(x_coords)),
            y_max=int(np.max(y_coords))
        )

        # 找到受影响的标线
        affected_lanes = []
        for lane_id, lane_segment in self.lane_segments.items():
            overlap = np.logical_and(mask, lane_segment.mask)
            if np.sum(overlap) > 0:
                affected_lanes.append(lane_id)

        occlusion_region = OcclusionRegion(
            occlusion_id=occlusion_id,
            occlusion_type=occlusion_type,
            mask=mask.copy(),
            bbox=bbox,
            confidence=confidence,
            affected_lanes=affected_lanes
        )

        self.occlusion_regions[occlusion_id] = occlusion_region
        return occlusion_id

    def get_combined_mask(self, lane_types: Optional[List[LaneType]] = None) -> np.ndarray:
        """获取组合mask"""
        combined_mask = np.zeros((self.height, self.width), dtype=np.uint8)

        for lane_segment in self.lane_segments.values():
            if lane_types is None or lane_segment.lane_type in lane_types:
                combined_mask = np.logical_or(combined_mask, lane_segment.mask)

        return combined_mask.astype(np.uint8)

    def get_occluded_mask(self) -> np.ndarray:
        """获取遮挡区域mask"""
        occluded_mask = np.zeros((self.height, self.width), dtype=np.uint8)

        for occlusion_region in self.occlusion_regions.values():
            occluded_mask = np.logical_or(occluded_mask, occlusion_region.mask)

        return occluded_mask.astype(np.uint8)

    def get_visible_mask(self) -> np.ndarray:
        """获取可见标线mask（排除遮挡区域）"""
        combined_mask = self.get_combined_mask()
        occluded_mask = self.get_occluded_mask()

        # 转换为布尔类型进行逻辑运算
        visible_mask = np.logical_and(combined_mask > 0, occluded_mask == 0)
        return visible_mask.astype(np.uint8)

"""
Mask数据模型
定义标线mask的数据结构和基本操作
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
from enum import Enum
import cv2


class LaneType(Enum):
    """标线类型枚举"""
    # 线性标记
    SOLID_LINE = "solid_line"                    # 0 - 单实线
    DASHED_LINE = "dashed_line"                  # 1 - 单虚线
    DOUBLE_LINE = "double_line"                  # 2 - 双实线
    STOP_LINE = "stop_line"                      # 3 - 停止线
    SLOW_LINE = "slow_line"                      # 4 - 减速线

    # 箭头标记
    ARROW_STRAIGHT = "arrow_straight"            # 5 - 直行箭头
    ARROW_LEFT = "arrow_left"                    # 6 - 左转箭头
    ARROW_RIGHT = "arrow_right"                  # 7 - 右转箭头
    ARROW_UTURN = "arrow_uturn"                  # 8 - 掉头箭头
    ARROW_STRAIGHT_LEFT = "arrow_straight_left"  # 9 - 直行+左转箭头
    ARROW_STRAIGHT_RIGHT = "arrow_straight_right" # 10 - 直行+右转箭头
    ARROW_STRAIGHT_UTURN = "arrow_straight_uturn" # 11 - 直行+掉头箭头
    ARROW_LEFT_UTURN = "arrow_left_uturn"        # 12 - 左转+掉头箭头

    # 符号标记
    ZEBRA = "zebra"                              # 13 - 人行横道（斑马线）
    TEXT = "text"                                # 14 - 道路文字及数字
    SYMBOL_MISC = "symbol_misc"                  # 15 - 其他符号
    DIVERGE_AREA = "diverge_area"                # 16 - 导流区
    GRID_AREA = "grid_area"                      # 17 - 网格区（禁止停车区）
    LEFT_RIGHT = "left_right"                    # 18 - 左右转箭头


class OcclusionType(Enum):
    """遮挡类型枚举"""
    DYNAMIC = "dynamic"    # 动态遮挡（车辆、行人）
    STATIC = "static"      # 静态遮挡（停车、设施）


# 标线类型到像素值的映射
LANE_TYPE_TO_PIXEL = {
    LaneType.SOLID_LINE: 3,
    LaneType.DASHED_LINE: 4,
    LaneType.DOUBLE_LINE: 5,
    LaneType.STOP_LINE: 6,
    LaneType.ARROW_STRAIGHT: 7,
    LaneType.DIVERGE_AREA: 8,
    LaneType.GRID_AREA: 9,
    LaneType.LEFT_RIGHT: 10,
    # 其他类型使用默认值
    LaneType.SLOW_LINE: 11,
    LaneType.ARROW_LEFT: 12,
    LaneType.ARROW_RIGHT: 13,
    LaneType.ARROW_UTURN: 14,
    LaneType.ARROW_STRAIGHT_LEFT: 15,
    LaneType.ARROW_STRAIGHT_RIGHT: 16,
    LaneType.ARROW_STRAIGHT_UTURN: 17,
    LaneType.ARROW_LEFT_UTURN: 18,
    LaneType.ZEBRA: 19,
    LaneType.TEXT: 20,
    LaneType.SYMBOL_MISC: 21,
}

# 像素值到标线类型的映射
PIXEL_TO_LANE_TYPE = {v: k for k, v in LANE_TYPE_TO_PIXEL.items()}

# 非标线类型（需要过滤的）
NON_LANE_PIXELS = {1, 2}  # car: 1, human: 2

# RGB颜色映射（用于可视化）
LANE_TYPE_COLORS = {
    LaneType.SOLID_LINE: (255, 255, 255),          # 白色
    LaneType.DASHED_LINE: (200, 200, 200),         # 浅灰色
    LaneType.DOUBLE_LINE: (255, 255, 255),         # 白色
    LaneType.STOP_LINE: (255, 255, 0),             # 黄色
    LaneType.SLOW_LINE: (255, 200, 0),             # 橙色
    LaneType.ARROW_STRAIGHT: (0, 255, 0),          # 绿色
    LaneType.ARROW_LEFT: (0, 255, 255),            # 青色
    LaneType.ARROW_RIGHT: (255, 0, 255),           # 洋红色
    LaneType.ARROW_UTURN: (255, 100, 100),         # 浅红色
    LaneType.ARROW_STRAIGHT_LEFT: (100, 255, 100), # 浅绿色
    LaneType.ARROW_STRAIGHT_RIGHT: (100, 100, 255), # 浅蓝色
    LaneType.ARROW_STRAIGHT_UTURN: (255, 255, 100), # 浅黄色
    LaneType.ARROW_LEFT_UTURN: (255, 100, 255),    # 浅洋红色
    LaneType.ZEBRA: (0, 0, 255),                   # 蓝色
    LaneType.TEXT: (128, 0, 128),                  # 紫色
    LaneType.SYMBOL_MISC: (128, 128, 0),           # 橄榄色
    LaneType.DIVERGE_AREA: (0, 128, 128),          # 深青色
    LaneType.GRID_AREA: (128, 128, 128),           # 灰色
    LaneType.LEFT_RIGHT: (255, 128, 0),            # 橙红色
}


@dataclass
class BoundingBox:
    """边界框数据结构"""
    x_min: int
    y_min: int
    x_max: int
    y_max: int

    @property
    def width(self) -> int:
        return self.x_max - self.x_min

    @property
    def height(self) -> int:
        return self.y_max - self.y_min

    @property
    def area(self) -> int:
        return self.width * self.height

    @property
    def center(self) -> Tuple[int, int]:
        return ((self.x_min + self.x_max) // 2, (self.y_min + self.y_max) // 2)


@dataclass
class LaneSegment:
    """标线段数据结构"""
    lane_id: int
    lane_type: LaneType
    mask: np.ndarray  # 二值mask
    bbox: BoundingBox
    confidence: float
    pixels: List[Tuple[int, int]]  # 像素坐标列表

    def get_skeleton(self) -> np.ndarray:
        """提取标线骨架"""
        from skimage.morphology import skeletonize
        return skeletonize(self.mask > 0)

    def get_endpoints(self) -> List[Tuple[int, int]]:
        """获取端点"""
        skeleton = self.get_skeleton()
        # 简化实现：找到骨架的端点
        kernel = np.ones((3, 3), np.uint8)
        neighbors = cv2.filter2D(skeleton.astype(np.uint8), -1, kernel)
        endpoints = np.where((skeleton > 0) & (neighbors == 2))
        return list(zip(endpoints[1], endpoints[0]))  # (x, y)格式

    def get_direction_vector(self) -> Optional[Tuple[float, float]]:
        """获取主方向向量"""
        if len(self.pixels) < 2:
            return None

        # 使用PCA计算主方向
        points = np.array(self.pixels)
        mean_point = np.mean(points, axis=0)
        centered_points = points - mean_point

        # 计算协方差矩阵
        cov_matrix = np.cov(centered_points.T)
        eigenvalues, eigenvectors = np.linalg.eig(cov_matrix)

        # 主方向是最大特征值对应的特征向量
        main_direction = eigenvectors[:, np.argmax(eigenvalues)]
        return tuple(main_direction)


@dataclass
class OcclusionRegion:
    """遮挡区域数据结构"""
    occlusion_id: int
    occlusion_type: OcclusionType
    mask: np.ndarray  # 遮挡区域mask
    bbox: BoundingBox
    confidence: float
    affected_lanes: List[int]  # 受影响的标线ID列表

    def get_overlap_with_lane(self, lane_segment: LaneSegment) -> float:
        """计算与标线段的重合度"""
        intersection = np.logical_and(self.mask, lane_segment.mask)
        union = np.logical_or(self.mask, lane_segment.mask)

        intersection_area = np.sum(intersection)
        union_area = np.sum(union)

        if union_area == 0:
            return 0.0

        return intersection_area / union_area


class LaneMask:
    """标线mask主类"""

    def __init__(self, image_shape: Tuple[int, int]):
        self.height, self.width = image_shape
        self.lane_segments: Dict[int, LaneSegment] = {}
        self.occlusion_regions: Dict[int, OcclusionRegion] = {}
        self.next_lane_id = 1
        self.next_occlusion_id = 1

    def add_lane_segment(self,
                        lane_type: LaneType,
                        mask: np.ndarray,
                        confidence: float = 1.0) -> int:
        """添加标线段"""
        lane_id = self.next_lane_id
        self.next_lane_id += 1

        # 计算边界框
        y_coords, x_coords = np.where(mask > 0)
        if len(x_coords) == 0:
            raise ValueError("Empty mask provided")

        bbox = BoundingBox(
            x_min=int(np.min(x_coords)),
            y_min=int(np.min(y_coords)),
            x_max=int(np.max(x_coords)),
            y_max=int(np.max(y_coords))
        )

        # 提取像素坐标
        pixels = list(zip(x_coords, y_coords))

        lane_segment = LaneSegment(
            lane_id=lane_id,
            lane_type=lane_type,
            mask=mask.copy(),
            bbox=bbox,
            confidence=confidence,
            pixels=pixels
        )

        self.lane_segments[lane_id] = lane_segment
        return lane_id

    def add_occlusion_region(self,
                           occlusion_type: OcclusionType,
                           mask: np.ndarray,
                           confidence: float = 1.0) -> int:
        """添加遮挡区域"""
        occlusion_id = self.next_occlusion_id
        self.next_occlusion_id += 1

        # 计算边界框
        y_coords, x_coords = np.where(mask > 0)
        if len(x_coords) == 0:
            raise ValueError("Empty occlusion mask provided")

        bbox = BoundingBox(
            x_min=int(np.min(x_coords)),
            y_min=int(np.min(y_coords)),
            x_max=int(np.max(x_coords)),
            y_max=int(np.max(y_coords))
        )

        # 找到受影响的标线
        affected_lanes = []
        for lane_id, lane_segment in self.lane_segments.items():
            overlap = np.logical_and(mask, lane_segment.mask)
            if np.sum(overlap) > 0:
                affected_lanes.append(lane_id)

        occlusion_region = OcclusionRegion(
            occlusion_id=occlusion_id,
            occlusion_type=occlusion_type,
            mask=mask.copy(),
            bbox=bbox,
            confidence=confidence,
            affected_lanes=affected_lanes
        )

        self.occlusion_regions[occlusion_id] = occlusion_region
        return occlusion_id

    def get_combined_mask(self, lane_types: Optional[List[LaneType]] = None) -> np.ndarray:
        """获取组合mask"""
        combined_mask = np.zeros((self.height, self.width), dtype=np.uint8)

        for lane_segment in self.lane_segments.values():
            if lane_types is None or lane_segment.lane_type in lane_types:
                combined_mask = np.logical_or(combined_mask, lane_segment.mask)

        return combined_mask.astype(np.uint8)

    def get_occluded_mask(self) -> np.ndarray:
        """获取遮挡区域mask"""
        occluded_mask = np.zeros((self.height, self.width), dtype=np.uint8)

        for occlusion_region in self.occlusion_regions.values():
            occluded_mask = np.logical_or(occluded_mask, occlusion_region.mask)

        return occluded_mask.astype(np.uint8)

    def get_visible_mask(self) -> np.ndarray:
        """获取可见标线mask（排除遮挡区域）"""
        combined_mask = self.get_combined_mask()
        occluded_mask = self.get_occluded_mask()

        # 转换为布尔类型进行逻辑运算
        visible_mask = np.logical_and(combined_mask > 0, occluded_mask == 0)
        return visible_mask.astype(np.uint8)

# 基于结构规则的遮挡恢复系统配置文件

# 数据路径配置
data:
  images_dir: "data/images"
  masks_dir: "data/masks"
  annotations_dir: "data/annotations"
  results_dir: "data/results"

# 标线分类配置
lane_classes:
  lines:
    - "single_solid"      # 单实线
    - "double_solid"      # 双实线
    - "dashed"           # 虚线
    - "stop_line"        # 停止线
    - "guide_line"       # 导流线
  arrows:
    - "left_turn"        # 左转箭头
    - "right_turn"       # 右转箭头
    - "straight"         # 直行箭头
    - "u_turn"           # 掉头箭头
    - "left_right"       # 左右转箭头
  symbols:
    - "crosswalk"        # 人行横道
    - "text_mark"        # 文字标记
    - "grid_mark"        # 网格标记
    - "guide_area"       # 导流区

# 遮挡检测配置
occlusion_detection:
  overlap_threshold: 0.3    # 重合度阈值
  min_occlusion_area: 50    # 最小遮挡面积(像素)
  confidence_threshold: 0.7  # 置信度阈值

# 规则推理配置
rule_engine:
  max_inference_distance: 100  # 最大推理距离(像素)
  geometric_tolerance: 5       # 几何容差(像素)
  semantic_weight: 0.7         # 语义权重
  geometric_weight: 0.3        # 几何权重

# 结构补全配置
structure_completion:
  interpolation_method: "cubic"  # 插值方法
  smoothing_factor: 0.1         # 平滑因子
  max_gap_length: 200           # 最大补全间隙长度(像素)

# 可视化配置
visualization:
  line_thickness: 2
  colors:
    original: [0, 255, 0]      # 绿色 - 原始标线
    occluded: [255, 0, 0]      # 红色 - 遮挡区域
    completed: [0, 0, 255]     # 蓝色 - 补全区域
    confidence_low: [255, 255, 0]   # 黄色 - 低置信度
    confidence_high: [0, 255, 255]  # 青色 - 高置信度

# 评估配置
evaluation:
  metrics:
    - "completeness"     # 完整性
    - "accuracy"         # 准确性
    - "consistency"      # 一致性
    - "robustness"       # 鲁棒性
  iou_threshold: 0.5     # IoU阈值
  pixel_tolerance: 3     # 像素容差

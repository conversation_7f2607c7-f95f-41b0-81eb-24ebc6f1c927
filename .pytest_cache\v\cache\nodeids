["tests/test_mask_model.py::TestBoundingBox::test_bbox_edge_cases", "tests/test_mask_model.py::TestBoundingBox::test_bbox_properties", "tests/test_mask_model.py::TestLaneMask::test_add_lane_segment", "tests/test_mask_model.py::TestLaneMask::test_add_lane_segment_empty_mask", "tests/test_mask_model.py::TestLaneMask::test_add_occlusion_region", "tests/test_mask_model.py::TestLaneMask::test_get_combined_mask", "tests/test_mask_model.py::TestLaneMask::test_get_combined_mask_with_filter", "tests/test_mask_model.py::TestLaneMask::test_get_visible_mask", "tests/test_mask_model.py::TestLaneMask::test_lane_mask_creation", "tests/test_mask_model.py::TestLaneSegment::test_get_direction_vector", "tests/test_mask_model.py::TestLaneSegment::test_get_endpoints", "tests/test_mask_model.py::TestLaneSegment::test_get_skeleton", "tests/test_mask_model.py::TestLaneSegment::test_lane_segment_creation", "tests/test_mask_model.py::TestOcclusionRegion::test_get_overlap_with_lane", "tests/test_mask_model.py::TestOcclusionRegion::test_occlusion_region_creation"]
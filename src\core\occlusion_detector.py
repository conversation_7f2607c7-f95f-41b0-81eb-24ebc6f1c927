"""
遮挡检测模块
检测标线与遮挡物的重合区域，识别遮挡类型和程度
"""

import numpy as np
import cv2
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import logging

from ..models.mask_model import LaneMask, LaneSegment, OcclusionRegion, OcclusionType, LaneType


@dataclass
class OcclusionAnalysis:
    """遮挡分析结果"""
    lane_id: int
    occlusion_id: int
    overlap_ratio: float  # 重合比例
    occlusion_area: int   # 遮挡面积
    confidence: float     # 检测置信度
    occlusion_type: OcclusionType


class OcclusionDetector:
    """遮挡检测器"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化遮挡检测器
        
        Args:
            config: 配置参数字典
        """
        self.config = config or {}
        self.overlap_threshold = self.config.get('overlap_threshold', 0.3)
        self.min_occlusion_area = self.config.get('min_occlusion_area', 50)
        self.confidence_threshold = self.config.get('confidence_threshold', 0.7)
        
        self.logger = logging.getLogger(__name__)
    
    def detect_occlusions(self, 
                         lane_mask: LaneMask,
                         occlusion_masks: Dict[str, np.ndarray],
                         occlusion_types: Dict[str, OcclusionType]) -> List[OcclusionAnalysis]:
        """
        检测遮挡区域
        
        Args:
            lane_mask: 标线mask对象
            occlusion_masks: 遮挡物mask字典 {name: mask}
            occlusion_types: 遮挡类型字典 {name: type}
            
        Returns:
            遮挡分析结果列表
        """
        occlusion_analyses = []
        
        # 为每个遮挡物创建遮挡区域
        for occlusion_name, occlusion_mask in occlusion_masks.items():
            occlusion_type = occlusion_types.get(occlusion_name, OcclusionType.DYNAMIC)
            
            # 添加遮挡区域到lane_mask
            try:
                occlusion_id = lane_mask.add_occlusion_region(
                    occlusion_type=occlusion_type,
                    mask=occlusion_mask,
                    confidence=1.0
                )
                
                # 分析每个标线段与此遮挡区域的关系
                occlusion_region = lane_mask.occlusion_regions[occlusion_id]
                
                for lane_id in occlusion_region.affected_lanes:
                    lane_segment = lane_mask.lane_segments[lane_id]
                    analysis = self._analyze_lane_occlusion(
                        lane_segment, occlusion_region
                    )
                    
                    if analysis.overlap_ratio >= self.overlap_threshold:
                        occlusion_analyses.append(analysis)
                        
            except ValueError as e:
                self.logger.warning(f"Failed to add occlusion region {occlusion_name}: {e}")
                continue
        
        return occlusion_analyses
    
    def _analyze_lane_occlusion(self, 
                               lane_segment: LaneSegment,
                               occlusion_region: OcclusionRegion) -> OcclusionAnalysis:
        """
        分析标线段的遮挡情况
        
        Args:
            lane_segment: 标线段
            occlusion_region: 遮挡区域
            
        Returns:
            遮挡分析结果
        """
        # 计算重合区域
        intersection = np.logical_and(lane_segment.mask, occlusion_region.mask)
        intersection_area = np.sum(intersection)
        
        # 计算重合比例（相对于标线段）
        lane_area = np.sum(lane_segment.mask)
        overlap_ratio = intersection_area / lane_area if lane_area > 0 else 0.0
        
        # 计算检测置信度
        confidence = self._calculate_detection_confidence(
            lane_segment, occlusion_region, intersection_area
        )
        
        return OcclusionAnalysis(
            lane_id=lane_segment.lane_id,
            occlusion_id=occlusion_region.occlusion_id,
            overlap_ratio=overlap_ratio,
            occlusion_area=intersection_area,
            confidence=confidence,
            occlusion_type=occlusion_region.occlusion_type
        )
    
    def _calculate_detection_confidence(self, 
                                      lane_segment: LaneSegment,
                                      occlusion_region: OcclusionRegion,
                                      intersection_area: int) -> float:
        """
        计算遮挡检测的置信度
        
        Args:
            lane_segment: 标线段
            occlusion_region: 遮挡区域
            intersection_area: 重合面积
            
        Returns:
            置信度值 [0, 1]
        """
        # 基础置信度：基于重合面积
        base_confidence = min(intersection_area / self.min_occlusion_area, 1.0)
        
        # 几何一致性：检查遮挡区域是否合理覆盖标线
        geometric_consistency = self._check_geometric_consistency(
            lane_segment, occlusion_region
        )
        
        # 类型一致性：某些标线类型更容易被遮挡
        type_consistency = self._get_type_consistency_factor(lane_segment.lane_type)
        
        # 综合置信度
        confidence = base_confidence * geometric_consistency * type_consistency
        
        return min(confidence, 1.0)
    
    def _check_geometric_consistency(self, 
                                   lane_segment: LaneSegment,
                                   occlusion_region: OcclusionRegion) -> float:
        """
        检查几何一致性
        
        Args:
            lane_segment: 标线段
            occlusion_region: 遮挡区域
            
        Returns:
            一致性分数 [0, 1]
        """
        # 检查遮挡区域的形状是否合理
        # 例如：车辆遮挡通常是矩形，行人遮挡通常是椭圆形
        
        # 计算遮挡区域的长宽比
        occlusion_bbox = occlusion_region.bbox
        aspect_ratio = occlusion_bbox.width / occlusion_bbox.height
        
        # 根据遮挡类型判断合理性
        if occlusion_region.occlusion_type == OcclusionType.DYNAMIC:
            # 动态遮挡（车辆）：长宽比通常在1.5-4之间
            if 1.5 <= aspect_ratio <= 4.0:
                return 1.0
            elif 1.0 <= aspect_ratio <= 6.0:
                return 0.7
            else:
                return 0.3
        else:
            # 静态遮挡：形状变化较大，给予中等置信度
            return 0.6
    
    def _get_type_consistency_factor(self, lane_type: LaneType) -> float:
        """
        获取标线类型的一致性因子
        
        Args:
            lane_type: 标线类型
            
        Returns:
            一致性因子 [0, 1]
        """
        # 不同类型的标线被遮挡的可能性不同
        type_factors = {
            LaneType.SINGLE_SOLID: 0.9,    # 单实线容易被遮挡
            LaneType.DOUBLE_SOLID: 0.9,    # 双实线容易被遮挡
            LaneType.DASHED: 0.8,          # 虚线部分遮挡较难检测
            LaneType.STOP_LINE: 0.95,      # 停止线经常被车辆遮挡
            LaneType.LEFT_TURN: 0.85,      # 箭头标记
            LaneType.RIGHT_TURN: 0.85,
            LaneType.STRAIGHT: 0.85,
            LaneType.U_TURN: 0.85,
            LaneType.CROSSWALK: 0.7,       # 人行横道较大，部分遮挡影响小
            LaneType.TEXT_MARK: 0.6,       # 文字标记复杂，遮挡检测困难
            LaneType.GRID_MARK: 0.6,       # 网格标记复杂
            LaneType.GUIDE_AREA: 0.5       # 导流区面积大，部分遮挡影响小
        }
        
        return type_factors.get(lane_type, 0.7)
    
    def filter_significant_occlusions(self, 
                                    occlusion_analyses: List[OcclusionAnalysis]) -> List[OcclusionAnalysis]:
        """
        过滤出显著的遮挡
        
        Args:
            occlusion_analyses: 遮挡分析结果列表
            
        Returns:
            过滤后的显著遮挡列表
        """
        significant_occlusions = []
        
        for analysis in occlusion_analyses:
            # 检查是否满足显著性条件
            if (analysis.overlap_ratio >= self.overlap_threshold and
                analysis.occlusion_area >= self.min_occlusion_area and
                analysis.confidence >= self.confidence_threshold):
                
                significant_occlusions.append(analysis)
        
        return significant_occlusions
    
    def create_occlusion_context(self, 
                               lane_segment: LaneSegment,
                               occlusion_analysis: OcclusionAnalysis,
                               lane_mask: LaneMask) -> Dict[str, any]:
        """
        创建遮挡上下文信息，用于规则推理
        
        Args:
            lane_segment: 标线段
            occlusion_analysis: 遮挡分析结果
            lane_mask: 完整的标线mask
            
        Returns:
            上下文信息字典
        """
        # 计算可见长度
        visible_mask = np.logical_and(lane_segment.mask, 
                                    ~lane_mask.occlusion_regions[occlusion_analysis.occlusion_id].mask)
        visible_pixels = np.sum(visible_mask)
        
        # 获取标线方向
        direction_vector = lane_segment.get_direction_vector()
        
        # 计算遮挡区域在标线上的位置（开始、中间、结束）
        occlusion_position = self._determine_occlusion_position(
            lane_segment, lane_mask.occlusion_regions[occlusion_analysis.occlusion_id]
        )
        
        context = {
            'lane_type': lane_segment.lane_type,
            'occlusion_ratio': occlusion_analysis.overlap_ratio,
            'visible_length': visible_pixels,
            'direction': direction_vector,
            'occlusion_position': occlusion_position,
            'occlusion_type': occlusion_analysis.occlusion_type,
            'lane_confidence': lane_segment.confidence,
            'occlusion_confidence': occlusion_analysis.confidence,
            'geometric': {
                'lane_bbox': lane_segment.bbox,
                'occlusion_bbox': lane_mask.occlusion_regions[occlusion_analysis.occlusion_id].bbox,
                'lane_area': np.sum(lane_segment.mask),
                'occlusion_area': occlusion_analysis.occlusion_area
            }
        }
        
        return context
    
    def _determine_occlusion_position(self, 
                                    lane_segment: LaneSegment,
                                    occlusion_region: OcclusionRegion) -> str:
        """
        确定遮挡在标线上的位置
        
        Args:
            lane_segment: 标线段
            occlusion_region: 遮挡区域
            
        Returns:
            位置描述: "start", "middle", "end", "full"
        """
        # 获取标线的端点
        endpoints = lane_segment.get_endpoints()
        if len(endpoints) < 2:
            return "unknown"
        
        # 计算遮挡区域中心
        occlusion_center = occlusion_region.bbox.center
        
        # 计算到各端点的距离
        distances = [
            np.sqrt((occlusion_center[0] - ep[0])**2 + (occlusion_center[1] - ep[1])**2)
            for ep in endpoints
        ]
        
        # 根据距离判断位置
        min_distance = min(distances)
        lane_length = np.sqrt(
            (endpoints[0][0] - endpoints[1][0])**2 + 
            (endpoints[0][1] - endpoints[1][1])**2
        )
        
        if min_distance < lane_length * 0.2:
            return "start" if distances[0] < distances[1] else "end"
        elif min_distance < lane_length * 0.8:
            return "middle"
        else:
            return "full"

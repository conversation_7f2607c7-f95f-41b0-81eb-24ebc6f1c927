"""
规则推理引擎
基于上下文信息匹配和应用推理规则
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from dataclasses import dataclass

from ..models.rule_model import RuleSet, InferenceRule, create_default_rules
from ..models.mask_model import LaneMask, LaneSegment, OcclusionRegion
from .occlusion_detector import OcclusionAnalysis


@dataclass
class InferenceResult:
    """推理结果"""
    lane_id: int
    occlusion_id: int
    rule_id: str
    completed_mask: np.ndarray
    confidence: float
    rule_confidence: float
    context: Dict[str, any]


class RuleEngine:
    """规则推理引擎"""
    
    def __init__(self, rule_set: Optional[RuleSet] = None, config: Optional[Dict] = None):
        """
        初始化规则引擎
        
        Args:
            rule_set: 规则集，如果为None则使用默认规则
            config: 配置参数
        """
        self.rule_set = rule_set or create_default_rules()
        self.config = config or {}
        
        # 配置参数
        self.max_inference_distance = self.config.get('max_inference_distance', 100)
        self.geometric_tolerance = self.config.get('geometric_tolerance', 5)
        self.semantic_weight = self.config.get('semantic_weight', 0.7)
        self.geometric_weight = self.config.get('geometric_weight', 0.3)
        
        self.logger = logging.getLogger(__name__)
    
    def infer_completions(self, 
                         lane_mask: LaneMask,
                         occlusion_analyses: List[OcclusionAnalysis],
                         contexts: List[Dict[str, any]]) -> List[InferenceResult]:
        """
        推理标线补全
        
        Args:
            lane_mask: 标线mask对象
            occlusion_analyses: 遮挡分析结果
            contexts: 上下文信息列表
            
        Returns:
            推理结果列表
        """
        inference_results = []
        
        for analysis, context in zip(occlusion_analyses, contexts):
            try:
                result = self._infer_single_completion(lane_mask, analysis, context)
                if result is not None:
                    inference_results.append(result)
            except Exception as e:
                self.logger.error(f"Failed to infer completion for lane {analysis.lane_id}: {e}")
                continue
        
        return inference_results
    
    def _infer_single_completion(self, 
                               lane_mask: LaneMask,
                               occlusion_analysis: OcclusionAnalysis,
                               context: Dict[str, any]) -> Optional[InferenceResult]:
        """
        推理单个标线的补全
        
        Args:
            lane_mask: 标线mask对象
            occlusion_analysis: 遮挡分析结果
            context: 上下文信息
            
        Returns:
            推理结果，如果无法推理则返回None
        """
        # 查找匹配的规则
        best_rule_match = self.rule_set.get_best_rule(context)
        
        if best_rule_match is None:
            self.logger.warning(f"No matching rule found for lane {occlusion_analysis.lane_id}")
            return None
        
        rule, rule_confidence = best_rule_match
        
        # 获取相关对象
        lane_segment = lane_mask.lane_segments[occlusion_analysis.lane_id]
        occlusion_region = lane_mask.occlusion_regions[occlusion_analysis.occlusion_id]
        
        # 应用规则进行补全
        completed_mask, completion_confidence = rule.apply(
            lane_segment, occlusion_region, context
        )
        
        # 计算综合置信度
        overall_confidence = self._calculate_overall_confidence(
            rule_confidence, completion_confidence, occlusion_analysis.confidence
        )
        
        # 验证补全结果
        if self._validate_completion(completed_mask, lane_segment, occlusion_region, context):
            return InferenceResult(
                lane_id=occlusion_analysis.lane_id,
                occlusion_id=occlusion_analysis.occlusion_id,
                rule_id=rule.rule_id,
                completed_mask=completed_mask,
                confidence=overall_confidence,
                rule_confidence=rule_confidence,
                context=context
            )
        else:
            self.logger.warning(f"Completion validation failed for lane {occlusion_analysis.lane_id}")
            return None
    
    def _calculate_overall_confidence(self, 
                                    rule_confidence: float,
                                    completion_confidence: float,
                                    detection_confidence: float) -> float:
        """
        计算综合置信度
        
        Args:
            rule_confidence: 规则匹配置信度
            completion_confidence: 补全执行置信度
            detection_confidence: 遮挡检测置信度
            
        Returns:
            综合置信度
        """
        # 使用加权几何平均
        weights = [0.4, 0.4, 0.2]  # 规则匹配和补全执行更重要
        confidences = [rule_confidence, completion_confidence, detection_confidence]
        
        # 几何平均
        product = 1.0
        for conf, weight in zip(confidences, weights):
            product *= conf ** weight
        
        return product
    
    def _validate_completion(self, 
                           completed_mask: np.ndarray,
                           lane_segment: LaneSegment,
                           occlusion_region: OcclusionRegion,
                           context: Dict[str, any]) -> bool:
        """
        验证补全结果的合理性
        
        Args:
            completed_mask: 补全的mask
            lane_segment: 原始标线段
            occlusion_region: 遮挡区域
            context: 上下文信息
            
        Returns:
            验证是否通过
        """
        # 检查1: 补全区域应该在遮挡区域内
        if not self._check_completion_within_occlusion(completed_mask, occlusion_region):
            return False
        
        # 检查2: 补全区域应该与原始标线连接
        if not self._check_completion_connectivity(completed_mask, lane_segment, occlusion_region):
            return False
        
        # 检查3: 补全结果的几何合理性
        if not self._check_geometric_reasonableness(completed_mask, lane_segment, context):
            return False
        
        # 检查4: 补全区域大小合理性
        if not self._check_completion_size(completed_mask, occlusion_region, context):
            return False
        
        return True
    
    def _check_completion_within_occlusion(self, 
                                         completed_mask: np.ndarray,
                                         occlusion_region: OcclusionRegion) -> bool:
        """检查补全区域是否在遮挡区域内"""
        # 补全区域应该主要在遮挡区域内
        intersection = np.logical_and(completed_mask, occlusion_region.mask)
        intersection_ratio = np.sum(intersection) / max(np.sum(completed_mask), 1)
        
        return intersection_ratio >= 0.8  # 至少80%在遮挡区域内
    
    def _check_completion_connectivity(self, 
                                     completed_mask: np.ndarray,
                                     lane_segment: LaneSegment,
                                     occlusion_region: OcclusionRegion) -> bool:
        """检查补全区域与原始标线的连接性"""
        # 获取可见的标线部分
        visible_lane = np.logical_and(lane_segment.mask, ~occlusion_region.mask)
        
        # 检查补全区域是否与可见部分相邻
        # 使用形态学膨胀检查邻接性
        kernel = np.ones((3, 3), np.uint8)
        dilated_visible = cv2.dilate(visible_lane.astype(np.uint8), kernel, iterations=1)
        
        # 检查补全区域是否与膨胀后的可见区域有重叠
        connection = np.logical_and(completed_mask, dilated_visible)
        
        return np.sum(connection) > 0
    
    def _check_geometric_reasonableness(self, 
                                      completed_mask: np.ndarray,
                                      lane_segment: LaneSegment,
                                      context: Dict[str, any]) -> bool:
        """检查几何合理性"""
        # 检查补全区域的形状是否与标线类型匹配
        lane_type = context.get('lane_type')
        
        if lane_type is None:
            return True
        
        # 计算补全区域的几何特征
        contours, _ = cv2.findContours(completed_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if len(contours) == 0:
            return False
        
        # 获取最大轮廓
        main_contour = max(contours, key=cv2.contourArea)
        
        # 计算长宽比
        rect = cv2.minAreaRect(main_contour)
        width, height = rect[1]
        aspect_ratio = max(width, height) / max(min(width, height), 1)
        
        # 根据标线类型检查合理性
        from ..models.mask_model import LaneType
        
        if lane_type in [LaneType.SINGLE_SOLID, LaneType.DOUBLE_SOLID, LaneType.DASHED]:
            # 线性标记应该是细长的
            return aspect_ratio >= 2.0
        elif lane_type in [LaneType.LEFT_TURN, LaneType.RIGHT_TURN, LaneType.STRAIGHT, LaneType.U_TURN]:
            # 箭头标记长宽比相对较小
            return 1.0 <= aspect_ratio <= 5.0
        else:
            # 其他类型给予较宽松的限制
            return aspect_ratio >= 1.0
    
    def _check_completion_size(self, 
                             completed_mask: np.ndarray,
                             occlusion_region: OcclusionRegion,
                             context: Dict[str, any]) -> bool:
        """检查补全区域大小的合理性"""
        completion_area = np.sum(completed_mask)
        occlusion_area = np.sum(occlusion_region.mask)
        
        # 补全区域不应该超过遮挡区域的大小
        if completion_area > occlusion_area:
            return False
        
        # 补全区域不应该太小（除非遮挡区域本身很小）
        min_completion_ratio = 0.1  # 至少占遮挡区域的10%
        if completion_area < occlusion_area * min_completion_ratio and occlusion_area > 100:
            return False
        
        return True
    
    def add_rule(self, rule: InferenceRule):
        """添加新规则"""
        self.rule_set.add_rule(rule)
    
    def get_rule_statistics(self) -> Dict[str, any]:
        """获取规则使用统计"""
        return {
            'total_rules': len(self.rule_set.rules),
            'rule_types': {rule_type.value: sum(1 for rule in self.rule_set.rules 
                                              if rule.rule_type == rule_type) 
                          for rule_type in set(rule.rule_type for rule in self.rule_set.rules)},
            'priority_distribution': {f'priority_{p}': sum(1 for rule in self.rule_set.rules 
                                                          if rule.priority == p)
                                    for p in set(rule.priority for rule in self.rule_set.rules)}
        }

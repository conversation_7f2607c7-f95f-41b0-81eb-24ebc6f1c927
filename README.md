# 基于结构规则的遮挡恢复与三维交通场景重建

## 🎯 项目概述

本项目旨在构建一个融合交通语义规则与视觉信息的系统，解决UAV图像中因动态与静态遮挡造成的交通标线缺失问题，实现基于规则推理的标线结构补全。

## 🧱 核心技术路径

**输入**: UAV影像 + 路面语义mask + 分类信息
**处理**: 遮挡检测 → 规则匹配 → 结构推理 → 补全验证
**输出**: 完整标线mask + 置信度评估

## 📁 项目结构

```
project/
├── data/                    # 数据目录
│   ├── images/             # UAV原始图像
│   ├── masks/              # 语义分割mask
│   ├── annotations/        # 标注数据
│   └── results/            # 处理结果
├── src/                    # 源代码
│   ├── core/               # 核心算法
│   │   ├── occlusion_detector.py    # 遮挡检测
│   │   ├── rule_engine.py           # 规则推理引擎
│   │   ├── structure_completer.py   # 结构补全
│   │   └── validator.py             # 一致性验证
│   ├── rules/              # 规则库
│   │   ├── rule_database.py         # 规则数据库
│   │   ├── lane_rules.json          # 车道线规则
│   │   ├── arrow_rules.json         # 箭头规则
│   │   └── symbol_rules.json        # 符号规则
│   ├── utils/              # 工具函数
│   │   ├── geometry.py              # 几何计算
│   │   ├── visualization.py         # 可视化
│   │   └── io_handler.py            # 数据IO
│   └── models/             # 数据模型
│       ├── mask_model.py            # Mask数据模型
│       └── rule_model.py            # 规则数据模型
├── tests/                  # 测试代码
├── configs/                # 配置文件
├── docs/                   # 文档
└── requirements.txt        # 依赖包
```

## 🚀 快速开始

### 环境配置
```bash
pip install -r requirements.txt
```

### 运行简单示例
```bash
# 运行基本示例（包含合成数据演示）
python examples/simple_example.py
```

### 基本使用
```python
from src.main import OcclusionRecoverySystem
from src.models.mask_model import LaneType, OcclusionType

# 初始化系统
system = OcclusionRecoverySystem("configs/config.yaml")

# 准备数据
lane_masks = {"lane_1": your_lane_mask}
lane_types = {"lane_1": LaneType.SINGLE_SOLID}
occlusion_masks = {"vehicle_1": your_occlusion_mask}
occlusion_types = {"vehicle_1": OcclusionType.DYNAMIC}

# 处理图像
result = system.process_image(
    image_path, lane_masks, lane_types,
    occlusion_masks, occlusion_types
)

# 查看结果
print(f"补全统计: {result['statistics']}")
print(f"详细报告: {result['report']}")
```

### 运行测试
```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_mask_model.py -v
```

## 📊 支持的标线类型

### 线性标记
- 单实线、双实线
- 虚线（各种间距模式）
- 停止线
- 导流线

### 箭头与符号
- 方向箭头（左转、右转、直行、掉头）
- 人行横道
- 文字标记
- 网格状标记

### 区域标记
- 导流区
- 禁停区
- 特殊功能区域

## 🧠 核心算法

### 1. 遮挡检测算法
- 基于像素重合度检测遮挡区域
- 区分动态遮挡与静态遮挡
- 生成遮挡置信度图

### 2. 规则推理引擎
- 基于可见部分特征匹配规则
- 多层级规则优先级管理
- 上下文感知的规则选择

### 3. 结构补全算法
- 几何约束下的结构延拓
- 语义一致性保证
- 多候选方案评估

## 📈 评估指标

- **完整性**: 补全区域的覆盖率
- **准确性**: 与真实标线的匹配度
- **一致性**: 语义和几何的一致性
- **鲁棒性**: 不同遮挡程度下的性能

## 🔬 实验设计

### 数据集
- UAV航拍交通场景图像
- 多种遮挡类型和程度
- 人工标注的完整标线真值

### 对比方法
- 传统图像修复方法
- 深度学习补全方法
- 基于模板的匹配方法

## 📝 开发计划

- [x] 项目框架搭建
- [ ] 遮挡检测模块
- [ ] 规则库设计与实现
- [ ] 规则推理引擎
- [ ] 结构补全算法
- [ ] 可视化与评估工具
- [ ] 实验验证与优化

## 📚 参考文献

相关研究论文和技术文档将在docs目录中维护。

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目。

## 📄 许可证

[待定]

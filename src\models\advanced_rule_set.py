"""
高级规则集
基于用户专业理解的完整规则系统
"""

from typing import Dict, List, Optional
from dataclasses import dataclass

from .rule_model import RuleSet, InferenceRule, RuleType, ConditionType, RuleCondition
from .advanced_rule_model import AdvancedCompletionAction
from .mask_model import LaneType


@dataclass
class AdvancedRuleSet:
    """高级规则集"""
    rules: Dict[str, InferenceRule]

    def __init__(self):
        self.rules = {}
        self._create_advanced_rules()

    def _create_advanced_rules(self):
        """创建高级规则"""

        # 1. 实线补全规则
        solid_line_rule = InferenceRule(
            rule_id="advanced_solid_line_001",
            rule_type=RuleType.LINE_EXTENSION,
            name="高级实线补全",
            description="基于边缘检测和曲度分析的实线补全",
            conditions=[
                RuleCondition(ConditionType.LANE_TYPE, "==", LaneType.SOLID_LINE, 1.0),
                RuleCondition(ConditionType.OCCLUSION_RATIO, "<", 0.8, 0.8),
                RuleCondition(ConditionType.VISIBLE_LENGTH, ">", 5, 0.6)
            ],
            actions=[
                AdvancedCompletionAction("solid_line_completion", {
                    "edge_detection": True,
                    "curvature_analysis": True,
                    "perspective_correction": True
                })
            ],
            priority=10,
            confidence_threshold=0.2
        )
        self.rules[solid_line_rule.rule_id] = solid_line_rule

        # 2. 双实线补全规则
        double_line_rule = InferenceRule(
            rule_id="advanced_double_line_001",
            rule_type=RuleType.LINE_EXTENSION,
            name="高级双实线补全",
            description="保持双线平行关系的补全",
            conditions=[
                RuleCondition(ConditionType.LANE_TYPE, "==", LaneType.DOUBLE_LINE, 1.0),
                RuleCondition(ConditionType.OCCLUSION_RATIO, "<", 0.8, 0.8),
                RuleCondition(ConditionType.VISIBLE_LENGTH, ">", 5, 0.6)
            ],
            actions=[
                AdvancedCompletionAction("solid_line_completion", {
                    "parallel_constraint": True,
                    "spacing_preservation": True,
                    "edge_detection": True
                })
            ],
            priority=10,
            confidence_threshold=0.2
        )
        self.rules[double_line_rule.rule_id] = double_line_rule

        # 3. 虚线补全规则
        dashed_line_rule = InferenceRule(
            rule_id="advanced_dashed_line_001",
            rule_type=RuleType.PATTERN_REPETITION,
            name="高级虚线补全",
            description="基于模式分析的虚线补全",
            conditions=[
                RuleCondition(ConditionType.LANE_TYPE, "==", LaneType.DASHED_LINE, 1.0),
                RuleCondition(ConditionType.VISIBLE_LENGTH, ">", 5, 0.7)
            ],
            actions=[
                AdvancedCompletionAction("dashed_line_completion", {
                    "pattern_analysis": True,
                    "segment_consistency": True,
                    "gap_calculation": True
                })
            ],
            priority=9,
            confidence_threshold=0.2
        )
        self.rules[dashed_line_rule.rule_id] = dashed_line_rule

        # 4. 停止线补全规则
        stop_line_rule = InferenceRule(
            rule_id="advanced_stop_line_001",
            rule_type=RuleType.PERPENDICULAR_COMPLETION,
            name="高级停止线补全",
            description="垂直于虚实线的停止线补全",
            conditions=[
                RuleCondition(ConditionType.LANE_TYPE, "==", LaneType.STOP_LINE, 1.0),
                RuleCondition(ConditionType.OCCLUSION_RATIO, "<", 0.8, 0.8),
                RuleCondition(ConditionType.VISIBLE_LENGTH, ">", 5, 0.6)
            ],
            actions=[
                AdvancedCompletionAction("stop_line_completion", {
                    "perpendicular_constraint": True,
                    "boundary_detection": True,
                    "adjacent_line_analysis": True
                })
            ],
            priority=8,
            confidence_threshold=0.3
        )
        self.rules[stop_line_rule.rule_id] = stop_line_rule

        # 5. 斑马线补全规则
        zebra_rule = InferenceRule(
            rule_id="advanced_zebra_001",
            rule_type=RuleType.PARALLEL_COMPLETION,
            name="高级斑马线补全",
            description="平行等间距的斑马线补全",
            conditions=[
                RuleCondition(ConditionType.LANE_TYPE, "==", LaneType.ZEBRA, 1.0),
                RuleCondition(ConditionType.VISIBLE_LENGTH, ">", 10, 0.7)
            ],
            actions=[
                AdvancedCompletionAction("zebra_completion", {
                    "parallel_analysis": True,
                    "spacing_consistency": True,
                    "length_uniformity": True
                })
            ],
            priority=7,
            confidence_threshold=0.3
        )
        self.rules[zebra_rule.rule_id] = zebra_rule

        # 6. 网格区补全规则
        grid_area_rule = InferenceRule(
            rule_id="advanced_grid_area_001",
            rule_type=RuleType.AREA_FILL,
            name="高级网格区补全",
            description="X型填充的网格区补全",
            conditions=[
                RuleCondition(ConditionType.LANE_TYPE, "==", LaneType.GRID_AREA, 1.0),
                RuleCondition(ConditionType.VISIBLE_LENGTH, ">", 20, 0.6)
            ],
            actions=[
                AdvancedCompletionAction("grid_area_completion", {
                    "boundary_detection": True,
                    "fill_pattern_analysis": True,
                    "boundary_constraint": True
                })
            ],
            priority=6,
            confidence_threshold=0.4
        )
        self.rules[grid_area_rule.rule_id] = grid_area_rule

        # 7. 导流区补全规则
        diverge_area_rule = InferenceRule(
            rule_id="advanced_diverge_area_001",
            rule_type=RuleType.AREA_FILL,
            name="高级导流区补全",
            description="V形或菱形填充的导流区补全",
            conditions=[
                RuleCondition(ConditionType.LANE_TYPE, "==", LaneType.DIVERGE_AREA, 1.0),
                RuleCondition(ConditionType.VISIBLE_LENGTH, ">", 15, 0.6)
            ],
            actions=[
                AdvancedCompletionAction("diverge_area_completion", {
                    "shape_analysis": True,
                    "parallel_fill": True,
                    "boundary_respect": True
                })
            ],
            priority=6,
            confidence_threshold=0.4
        )
        self.rules[diverge_area_rule.rule_id] = diverge_area_rule

        # 8. 箭头补全规则
        arrow_rule = InferenceRule(
            rule_id="advanced_arrow_001",
            rule_type=RuleType.SHAPE_COMPLETION,
            name="高级箭头补全",
            description="基于形状分析的箭头补全",
            conditions=[
                RuleCondition(ConditionType.LANE_TYPE, "in", [
                    LaneType.ARROW_STRAIGHT, LaneType.ARROW_LEFT, LaneType.ARROW_RIGHT,
                    LaneType.LEFT_RIGHT, LaneType.ARROW_STRAIGHT_LEFT, LaneType.ARROW_STRAIGHT_RIGHT
                ], 1.0),
                RuleCondition(ConditionType.VISIBLE_LENGTH, ">", 10, 0.6)
            ],
            actions=[
                AdvancedCompletionAction("arrow_completion", {
                    "shape_recognition": True,
                    "symmetry_analysis": True,
                    "direction_preservation": True
                })
            ],
            priority=5,
            confidence_threshold=0.5
        )
        self.rules[arrow_rule.rule_id] = arrow_rule

    def find_matching_rules(self, context: Dict[str, any]) -> List[tuple]:
        """
        查找匹配的规则

        Args:
            context: 上下文信息

        Returns:
            匹配的规则和置信度列表
        """
        matching_rules = []

        for rule in self.rules.values():
            confidence = self._evaluate_rule_conditions(rule, context)
            if confidence >= rule.confidence_threshold:
                matching_rules.append((rule, confidence))

        # 按优先级和置信度排序
        matching_rules.sort(key=lambda x: (x[0].priority, x[1]), reverse=True)

        return matching_rules

    def _evaluate_rule_conditions(self, rule: InferenceRule, context: Dict[str, any]) -> float:
        """评估规则条件"""
        if not rule.conditions:
            return 0.0

        total_confidence = 0.0
        total_weight = 0.0

        for condition in rule.conditions:
            confidence = self._evaluate_condition(condition, context)
            total_confidence += confidence * condition.weight
            total_weight += condition.weight

        return total_confidence / total_weight if total_weight > 0 else 0.0

    def _evaluate_condition(self, condition: RuleCondition, context: Dict[str, any]) -> float:
        """评估单个条件"""
        context_value = context.get(condition.condition_type.value)

        if context_value is None:
            return 0.0

        if condition.operator == "==":
            return 1.0 if context_value == condition.value else 0.0
        elif condition.operator == "!=":
            return 1.0 if context_value != condition.value else 0.0
        elif condition.operator == ">":
            return 1.0 if context_value > condition.value else 0.0
        elif condition.operator == "<":
            return 1.0 if context_value < condition.value else 0.0
        elif condition.operator == ">=":
            return 1.0 if context_value >= condition.value else 0.0
        elif condition.operator == "<=":
            return 1.0 if context_value <= condition.value else 0.0
        elif condition.operator == "in":
            return 1.0 if context_value in condition.value else 0.0
        else:
            return 0.0


def create_advanced_rule_set() -> AdvancedRuleSet:
    """创建高级规则集"""
    return AdvancedRuleSet()

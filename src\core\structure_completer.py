"""
结构补全模块
整合推理结果，生成最终的完整标线mask
"""

import numpy as np
import cv2
from typing import Dict, List, Tuple, Optional
import logging
from dataclasses import dataclass

from ..models.mask_model import LaneMask, LaneType
from .rule_engine import InferenceResult


@dataclass
class CompletionResult:
    """补全结果"""
    original_mask: np.ndarray
    completed_mask: np.ndarray
    completion_regions: np.ndarray  # 仅补全的区域
    confidence_map: np.ndarray
    statistics: Dict[str, any]


class StructureCompleter:
    """结构补全器"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化结构补全器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        
        # 配置参数
        self.interpolation_method = self.config.get('interpolation_method', 'cubic')
        self.smoothing_factor = self.config.get('smoothing_factor', 0.1)
        self.max_gap_length = self.config.get('max_gap_length', 200)
        self.min_confidence_threshold = self.config.get('min_confidence_threshold', 0.3)
        
        self.logger = logging.getLogger(__name__)
    
    def complete_structure(self, 
                          lane_mask: LaneMask,
                          inference_results: List[InferenceResult]) -> CompletionResult:
        """
        完成结构补全
        
        Args:
            lane_mask: 原始标线mask
            inference_results: 推理结果列表
            
        Returns:
            补全结果
        """
        # 获取原始mask
        original_mask = lane_mask.get_combined_mask()
        
        # 初始化补全mask
        completed_mask = original_mask.copy()
        completion_regions = np.zeros_like(original_mask)
        confidence_map = np.ones_like(original_mask, dtype=np.float32)
        
        # 统计信息
        statistics = {
            'total_inferences': len(inference_results),
            'successful_completions': 0,
            'failed_completions': 0,
            'total_completed_pixels': 0,
            'average_confidence': 0.0,
            'completion_by_type': {}
        }
        
        # 按置信度排序推理结果
        sorted_results = sorted(inference_results, key=lambda x: x.confidence, reverse=True)
        
        # 逐个应用推理结果
        for result in sorted_results:
            if result.confidence < self.min_confidence_threshold:
                statistics['failed_completions'] += 1
                continue
            
            try:
                success = self._apply_inference_result(
                    result, completed_mask, completion_regions, confidence_map, lane_mask
                )
                
                if success:
                    statistics['successful_completions'] += 1
                    statistics['total_completed_pixels'] += np.sum(result.completed_mask)
                    
                    # 按类型统计
                    lane_segment = lane_mask.lane_segments[result.lane_id]
                    lane_type = lane_segment.lane_type.value
                    if lane_type not in statistics['completion_by_type']:
                        statistics['completion_by_type'][lane_type] = 0
                    statistics['completion_by_type'][lane_type] += 1
                else:
                    statistics['failed_completions'] += 1
                    
            except Exception as e:
                self.logger.error(f"Failed to apply inference result {result.rule_id}: {e}")
                statistics['failed_completions'] += 1
                continue
        
        # 后处理：平滑和优化
        completed_mask = self._post_process_mask(completed_mask, completion_regions)
        
        # 计算平均置信度
        if statistics['successful_completions'] > 0:
            total_confidence = sum(r.confidence for r in sorted_results 
                                 if r.confidence >= self.min_confidence_threshold)
            statistics['average_confidence'] = total_confidence / statistics['successful_completions']
        
        return CompletionResult(
            original_mask=original_mask,
            completed_mask=completed_mask,
            completion_regions=completion_regions,
            confidence_map=confidence_map,
            statistics=statistics
        )
    
    def _apply_inference_result(self, 
                              result: InferenceResult,
                              completed_mask: np.ndarray,
                              completion_regions: np.ndarray,
                              confidence_map: np.ndarray,
                              lane_mask: LaneMask) -> bool:
        """
        应用单个推理结果
        
        Args:
            result: 推理结果
            completed_mask: 完整mask（会被修改）
            completion_regions: 补全区域mask（会被修改）
            confidence_map: 置信度图（会被修改）
            lane_mask: 原始标线mask
            
        Returns:
            是否成功应用
        """
        try:
            # 检查冲突
            if self._check_conflicts(result.completed_mask, completed_mask, lane_mask):
                self.logger.warning(f"Conflict detected for inference result {result.rule_id}")
                return False
            
            # 获取遮挡区域
            occlusion_region = lane_mask.occlusion_regions[result.occlusion_id]
            
            # 只在遮挡区域内应用补全
            valid_completion = np.logical_and(result.completed_mask, occlusion_region.mask)
            
            if np.sum(valid_completion) == 0:
                return False
            
            # 更新mask
            completed_mask[valid_completion] = 1
            completion_regions[valid_completion] = 1
            
            # 更新置信度图
            confidence_map[valid_completion] = result.confidence
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error applying inference result: {e}")
            return False
    
    def _check_conflicts(self, 
                        new_completion: np.ndarray,
                        existing_mask: np.ndarray,
                        lane_mask: LaneMask) -> bool:
        """
        检查新补全是否与现有结果冲突
        
        Args:
            new_completion: 新的补全mask
            existing_mask: 现有的完整mask
            lane_mask: 原始标线mask
            
        Returns:
            是否存在冲突
        """
        # 检查是否与现有补全重叠（在非原始标线区域）
        original_mask = lane_mask.get_combined_mask()
        existing_completion = np.logical_and(existing_mask, ~original_mask)
        
        # 计算重叠
        overlap = np.logical_and(new_completion, existing_completion)
        overlap_ratio = np.sum(overlap) / max(np.sum(new_completion), 1)
        
        # 如果重叠超过30%，认为存在冲突
        return overlap_ratio > 0.3
    
    def _post_process_mask(self, 
                          completed_mask: np.ndarray,
                          completion_regions: np.ndarray) -> np.ndarray:
        """
        后处理mask：平滑、去噪、连接
        
        Args:
            completed_mask: 完整mask
            completion_regions: 补全区域mask
            
        Returns:
            后处理后的mask
        """
        processed_mask = completed_mask.copy()
        
        # 1. 形态学操作去除噪声
        kernel = np.ones((3, 3), np.uint8)
        processed_mask = cv2.morphologyEx(processed_mask, cv2.MORPH_CLOSE, kernel)
        processed_mask = cv2.morphologyEx(processed_mask, cv2.MORPH_OPEN, kernel)
        
        # 2. 平滑补全区域的边界
        if np.sum(completion_regions) > 0:
            processed_mask = self._smooth_completion_boundaries(
                processed_mask, completion_regions
            )
        
        # 3. 连接近距离的断点
        processed_mask = self._connect_nearby_segments(processed_mask)
        
        return processed_mask
    
    def _smooth_completion_boundaries(self, 
                                    mask: np.ndarray,
                                    completion_regions: np.ndarray) -> np.ndarray:
        """
        平滑补全区域的边界
        
        Args:
            mask: 完整mask
            completion_regions: 补全区域mask
            
        Returns:
            平滑后的mask
        """
        # 对补全区域进行高斯平滑
        if self.smoothing_factor > 0:
            # 提取补全区域
            completion_mask = np.logical_and(mask, completion_regions).astype(np.float32)
            
            # 高斯平滑
            kernel_size = max(3, int(self.smoothing_factor * 10))
            if kernel_size % 2 == 0:
                kernel_size += 1
            
            smoothed = cv2.GaussianBlur(completion_mask, (kernel_size, kernel_size), 
                                      self.smoothing_factor * 5)
            
            # 阈值化回二值
            smoothed_binary = (smoothed > 0.5).astype(np.uint8)
            
            # 只更新补全区域
            result_mask = mask.copy()
            result_mask[completion_regions > 0] = smoothed_binary[completion_regions > 0]
            
            return result_mask
        
        return mask
    
    def _connect_nearby_segments(self, mask: np.ndarray) -> np.ndarray:
        """
        连接近距离的线段
        
        Args:
            mask: 输入mask
            
        Returns:
            连接后的mask
        """
        # 查找轮廓
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if len(contours) < 2:
            return mask
        
        result_mask = mask.copy()
        
        # 对每对轮廓检查是否需要连接
        for i in range(len(contours)):
            for j in range(i + 1, len(contours)):
                if self._should_connect_contours(contours[i], contours[j]):
                    # 连接两个轮廓
                    self._connect_contours(result_mask, contours[i], contours[j])
        
        return result_mask
    
    def _should_connect_contours(self, contour1: np.ndarray, contour2: np.ndarray) -> bool:
        """
        判断是否应该连接两个轮廓
        
        Args:
            contour1: 轮廓1
            contour2: 轮廓2
            
        Returns:
            是否应该连接
        """
        # 计算轮廓间的最小距离
        min_distance = float('inf')
        
        for pt1 in contour1:
            for pt2 in contour2:
                distance = np.sqrt((pt1[0][0] - pt2[0][0])**2 + (pt1[0][1] - pt2[0][1])**2)
                min_distance = min(min_distance, distance)
        
        # 如果距离小于阈值，则连接
        connection_threshold = 10  # 像素
        return min_distance < connection_threshold
    
    def _connect_contours(self, 
                         mask: np.ndarray,
                         contour1: np.ndarray,
                         contour2: np.ndarray):
        """
        连接两个轮廓
        
        Args:
            mask: 要修改的mask
            contour1: 轮廓1
            contour2: 轮廓2
        """
        # 找到最近的点对
        min_distance = float('inf')
        closest_points = None
        
        for pt1 in contour1:
            for pt2 in contour2:
                distance = np.sqrt((pt1[0][0] - pt2[0][0])**2 + (pt1[0][1] - pt2[0][1])**2)
                if distance < min_distance:
                    min_distance = distance
                    closest_points = (tuple(pt1[0]), tuple(pt2[0]))
        
        if closest_points is not None:
            # 在两点间画线连接
            cv2.line(mask, closest_points[0], closest_points[1], 1, thickness=2)
    
    def generate_completion_report(self, result: CompletionResult) -> Dict[str, any]:
        """
        生成补全报告
        
        Args:
            result: 补全结果
            
        Returns:
            详细报告
        """
        original_pixels = np.sum(result.original_mask)
        completed_pixels = np.sum(result.completed_mask)
        completion_pixels = np.sum(result.completion_regions)
        
        report = {
            'summary': {
                'original_pixels': int(original_pixels),
                'completed_pixels': int(completed_pixels),
                'completion_pixels': int(completion_pixels),
                'completion_ratio': float(completion_pixels / max(original_pixels, 1)),
                'total_improvement': float((completed_pixels - original_pixels) / max(original_pixels, 1))
            },
            'statistics': result.statistics,
            'confidence': {
                'mean_confidence': float(np.mean(result.confidence_map[result.completion_regions > 0])) 
                                 if np.sum(result.completion_regions) > 0 else 0.0,
                'min_confidence': float(np.min(result.confidence_map[result.completion_regions > 0])) 
                                if np.sum(result.completion_regions) > 0 else 0.0,
                'max_confidence': float(np.max(result.confidence_map[result.completion_regions > 0])) 
                                if np.sum(result.completion_regions) > 0 else 0.0
            }
        }
        
        return report

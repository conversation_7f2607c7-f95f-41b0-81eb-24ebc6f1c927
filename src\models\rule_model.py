"""
规则数据模型
定义推理规则的数据结构
"""

from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass, field
from enum import Enum
import numpy as np
from .mask_model import LaneType, LaneSegment, OcclusionRegion


class RuleType(Enum):
    """规则类型枚举"""
    LINE_EXTENSION = "line_extension"        # 线段延伸
    SYMMETRY_COMPLETION = "symmetry_completion"  # 对称补全
    PATTERN_REPETITION = "pattern_repetition"    # 模式重复
    INTERSECTION_INFERENCE = "intersection_inference"  # 交叉口推理
    CONTEXTUAL_COMPLETION = "contextual_completion"    # 上下文补全


class ConditionType(Enum):
    """条件类型枚举"""
    LANE_TYPE = "lane_type"              # 标线类型
    OCCLUSION_RATIO = "occlusion_ratio"  # 遮挡比例
    VISIBLE_LENGTH = "visible_length"    # 可见长度
    DIRECTION = "direction"              # 方向
    PROXIMITY = "proximity"              # 邻近性
    GEOMETRIC = "geometric"              # 几何特征


@dataclass
class RuleCondition:
    """规则条件"""
    condition_type: ConditionType
    operator: str  # "==", "!=", ">", "<", ">=", "<=", "in", "not_in"
    value: Any
    weight: float = 1.0
    
    def evaluate(self, context: Dict[str, Any]) -> Tuple[bool, float]:
        """评估条件是否满足"""
        if self.condition_type.value not in context:
            return False, 0.0
        
        context_value = context[self.condition_type.value]
        
        try:
            if self.operator == "==":
                result = context_value == self.value
            elif self.operator == "!=":
                result = context_value != self.value
            elif self.operator == ">":
                result = context_value > self.value
            elif self.operator == "<":
                result = context_value < self.value
            elif self.operator == ">=":
                result = context_value >= self.value
            elif self.operator == "<=":
                result = context_value <= self.value
            elif self.operator == "in":
                result = context_value in self.value
            elif self.operator == "not_in":
                result = context_value not in self.value
            else:
                return False, 0.0
            
            confidence = self.weight if result else 0.0
            return result, confidence
            
        except (TypeError, ValueError):
            return False, 0.0


@dataclass
class CompletionAction:
    """补全动作"""
    action_type: str  # "extend", "interpolate", "mirror", "pattern_fill"
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    def execute(self, 
                lane_segment: LaneSegment, 
                occlusion_region: OcclusionRegion,
                context: Dict[str, Any]) -> np.ndarray:
        """执行补全动作"""
        if self.action_type == "extend":
            return self._extend_line(lane_segment, occlusion_region, context)
        elif self.action_type == "interpolate":
            return self._interpolate_gap(lane_segment, occlusion_region, context)
        elif self.action_type == "mirror":
            return self._mirror_symmetry(lane_segment, occlusion_region, context)
        elif self.action_type == "pattern_fill":
            return self._pattern_fill(lane_segment, occlusion_region, context)
        else:
            raise ValueError(f"Unknown action type: {self.action_type}")
    
    def _extend_line(self, 
                    lane_segment: LaneSegment, 
                    occlusion_region: OcclusionRegion,
                    context: Dict[str, Any]) -> np.ndarray:
        """线段延伸"""
        # 获取标线方向
        direction = lane_segment.get_direction_vector()
        if direction is None:
            return np.zeros_like(occlusion_region.mask)
        
        # 获取延伸参数
        extension_length = self.parameters.get("extension_length", 50)
        line_width = self.parameters.get("line_width", 3)
        
        # 找到与遮挡区域相交的边界点
        intersection_mask = np.logical_and(lane_segment.mask, occlusion_region.mask)
        if not np.any(intersection_mask):
            return np.zeros_like(occlusion_region.mask)
        
        # 简化实现：在遮挡区域内沿方向延伸
        result_mask = np.zeros_like(occlusion_region.mask)
        
        # 这里需要实现具体的线段延伸算法
        # 暂时返回空mask作为占位符
        return result_mask
    
    def _interpolate_gap(self, 
                        lane_segment: LaneSegment, 
                        occlusion_region: OcclusionRegion,
                        context: Dict[str, Any]) -> np.ndarray:
        """间隙插值"""
        # 实现间隙插值算法
        return np.zeros_like(occlusion_region.mask)
    
    def _mirror_symmetry(self, 
                        lane_segment: LaneSegment, 
                        occlusion_region: OcclusionRegion,
                        context: Dict[str, Any]) -> np.ndarray:
        """对称镜像"""
        # 实现对称镜像算法
        return np.zeros_like(occlusion_region.mask)
    
    def _pattern_fill(self, 
                     lane_segment: LaneSegment, 
                     occlusion_region: OcclusionRegion,
                     context: Dict[str, Any]) -> np.ndarray:
        """模式填充"""
        # 实现模式填充算法
        return np.zeros_like(occlusion_region.mask)


@dataclass
class InferenceRule:
    """推理规则"""
    rule_id: str
    rule_type: RuleType
    name: str
    description: str
    conditions: List[RuleCondition]
    actions: List[CompletionAction]
    priority: int = 1
    confidence_threshold: float = 0.5
    
    def match(self, context: Dict[str, Any]) -> Tuple[bool, float]:
        """匹配规则条件"""
        total_weight = sum(condition.weight for condition in self.conditions)
        if total_weight == 0:
            return False, 0.0
        
        satisfied_weight = 0.0
        all_satisfied = True
        
        for condition in self.conditions:
            satisfied, confidence = condition.evaluate(context)
            if satisfied:
                satisfied_weight += confidence
            else:
                all_satisfied = False
        
        # 计算总体置信度
        overall_confidence = satisfied_weight / total_weight
        
        # 规则匹配需要所有条件都满足，且总体置信度超过阈值
        is_matched = all_satisfied and overall_confidence >= self.confidence_threshold
        
        return is_matched, overall_confidence
    
    def apply(self, 
             lane_segment: LaneSegment, 
             occlusion_region: OcclusionRegion,
             context: Dict[str, Any]) -> Tuple[np.ndarray, float]:
        """应用规则进行补全"""
        is_matched, confidence = self.match(context)
        
        if not is_matched:
            return np.zeros_like(occlusion_region.mask), 0.0
        
        # 执行所有动作并合并结果
        result_mask = np.zeros_like(occlusion_region.mask)
        
        for action in self.actions:
            action_result = action.execute(lane_segment, occlusion_region, context)
            result_mask = np.logical_or(result_mask, action_result)
        
        return result_mask.astype(np.uint8), confidence


@dataclass
class RuleSet:
    """规则集合"""
    rules: List[InferenceRule] = field(default_factory=list)
    
    def add_rule(self, rule: InferenceRule):
        """添加规则"""
        self.rules.append(rule)
        # 按优先级排序
        self.rules.sort(key=lambda r: r.priority, reverse=True)
    
    def find_matching_rules(self, context: Dict[str, Any]) -> List[Tuple[InferenceRule, float]]:
        """查找匹配的规则"""
        matching_rules = []
        
        for rule in self.rules:
            is_matched, confidence = rule.match(context)
            if is_matched:
                matching_rules.append((rule, confidence))
        
        # 按置信度排序
        matching_rules.sort(key=lambda x: x[1], reverse=True)
        return matching_rules
    
    def get_best_rule(self, context: Dict[str, Any]) -> Optional[Tuple[InferenceRule, float]]:
        """获取最佳匹配规则"""
        matching_rules = self.find_matching_rules(context)
        return matching_rules[0] if matching_rules else None


def create_default_rules() -> RuleSet:
    """创建默认规则集"""
    rule_set = RuleSet()
    
    # 规则1: 单实线延伸
    single_line_extension = InferenceRule(
        rule_id="single_line_ext_001",
        rule_type=RuleType.LINE_EXTENSION,
        name="单实线延伸",
        description="当单实线被部分遮挡时，沿原方向延伸",
        conditions=[
            RuleCondition(ConditionType.LANE_TYPE, "==", LaneType.SINGLE_SOLID, 1.0),
            RuleCondition(ConditionType.OCCLUSION_RATIO, "<", 0.8, 0.8),
            RuleCondition(ConditionType.VISIBLE_LENGTH, ">", 20, 0.6)
        ],
        actions=[
            CompletionAction("extend", {"extension_length": 100, "line_width": 3})
        ],
        priority=5,
        confidence_threshold=0.7
    )
    
    # 规则2: 虚线模式重复
    dashed_line_pattern = InferenceRule(
        rule_id="dashed_pattern_001",
        rule_type=RuleType.PATTERN_REPETITION,
        name="虚线模式重复",
        description="根据可见虚线段推断遮挡区域的虚线模式",
        conditions=[
            RuleCondition(ConditionType.LANE_TYPE, "==", LaneType.DASHED, 1.0),
            RuleCondition(ConditionType.VISIBLE_LENGTH, ">", 30, 0.7)
        ],
        actions=[
            CompletionAction("pattern_fill", {"pattern_type": "dashed"})
        ],
        priority=4,
        confidence_threshold=0.6
    )
    
    # 规则3: 箭头对称补全
    arrow_symmetry = InferenceRule(
        rule_id="arrow_symmetry_001",
        rule_type=RuleType.SYMMETRY_COMPLETION,
        name="箭头对称补全",
        description="基于箭头的对称性补全缺失部分",
        conditions=[
            RuleCondition(ConditionType.LANE_TYPE, "in", 
                         [LaneType.LEFT_TURN, LaneType.RIGHT_TURN, LaneType.STRAIGHT], 1.0),
            RuleCondition(ConditionType.OCCLUSION_RATIO, "<", 0.6, 0.8)
        ],
        actions=[
            CompletionAction("mirror", {"symmetry_axis": "auto"})
        ],
        priority=6,
        confidence_threshold=0.7
    )
    
    rule_set.add_rule(single_line_extension)
    rule_set.add_rule(dashed_line_pattern)
    rule_set.add_rule(arrow_symmetry)
    
    return rule_set
